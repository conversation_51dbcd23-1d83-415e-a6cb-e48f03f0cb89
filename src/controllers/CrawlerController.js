/**
 * 爬虫控制器
 *
 * 功能说明：
 * - 管理爬虫任务的创建、执行、暂停、恢复和删除
 * - 提供爬虫结果的查询、筛选和导入功能
 * - 支持爬虫任务的重试和批量操作
 * - 监控爬虫服务状态和任务执行进度
 *
 * 主要接口：
 * - POST /api/crawler/tasks - 创建爬虫任务
 * - GET /api/crawler/tasks - 获取任务列表
 * - GET /api/crawler/tasks/:id - 获取任务详情
 * - POST /api/crawler/tasks/:id/start - 启动任务
 * - POST /api/crawler/tasks/:id/pause - 暂停任务
 * - POST /api/crawler/tasks/:id/retry - 重试任务
 * - DELETE /api/crawler/tasks/:id - 删除任务
 * - GET /api/crawler/results - 获取爬虫结果
 * - POST /api/crawler/results/import - 导入结果到达人库
 * - GET /api/crawler/status - 获取服务状态
 *
 * 支持的平台：
 * - xiaohongshu: 小红书平台
 * - juxingtu: 巨量星图平台
 *
 * <AUTHOR>
 * @version 1.0.0
 */

const crawlerService = require('../services/crawler');
const { CrawlTask, PublicInfluencer, CrawlLog } = require('../models');
const { Op, sequelize } = require('sequelize');
const { sequelize: dbInstance } = require('../models');
const DateFormatter = require('../utils/dateFormatter');
const ResponseUtil = require('../utils/response');

class CrawlerController {
  /**
   * 创建爬虫任务
   *
   * 功能说明：
   * - 创建新的爬虫任务并添加到任务队列
   * - 验证必填字段和参数有效性
   * - 自动设置任务创建者信息
   * - 支持自定义爬取配置和优先级
   *
   * 请求体参数：
   * @param {string} taskName - 任务名称（必填）
   * @param {string} platform - 平台类型（必填，xiaohongshu/juxingtu）
   * @param {string} keywords - 搜索关键词（必填）
   * @param {Object} config - 爬取配置（可选）
   * @param {number} priority - 任务优先级，默认0
   * @param {number} maxPages - 最大爬取页数，默认5
   *
   * 响应格式：
   * @returns {Object} 响应对象
   * @returns {boolean} success - 操作是否成功
   * @returns {string} message - 响应消息
   * @returns {Object} data - 创建的任务信息
   *
   * 错误情况：
   * - 400: 必填字段缺失或参数无效
   * - 500: 服务器内部错误
   *
   * 使用示例：
   * POST /api/crawler/tasks
   * {
   *   "taskName": "美妆达人搜索",
   *   "platform": "xiaohongshu",
   *   "keywords": "美妆 护肤",
   *   "config": {"searchType": "user"},
   *   "priority": 1,
   *   "maxPages": 10
   * }
   */
  async createTask(ctx) {
    try {
      const { taskName, platform, keywords, config, priority, maxPages } = ctx.request.body;
      const userId = ctx.state.user.id;

      // 调试日志：打印接收到的请求数据
      console.log('🔍 [CrawlerController] 接收到创建任务请求:');
      console.log('   taskName:', taskName);
      console.log('   platform:', platform);
      console.log('   keywords:', keywords);
      console.log('   config:', JSON.stringify(config, null, 2));
      console.log('   priority:', priority);
      console.log('   maxPages:', maxPages);
      console.log('   minFirstNotePlayCount:', config?.minFirstNotePlayCount);

      // 验证必填字段
      if (!taskName) {
        ctx.throw(400, '任务名称不能为空');
      }
      if (!platform) {
        ctx.throw(400, '平台类型不能为空');
      }
      if (!keywords) {
        ctx.throw(400, '搜索关键词不能为空');
      }

      // 验证和处理配置参数
      const validatedConfig = this.validateAndProcessConfig(config);

      // 创建任务
      const task = await crawlerService.createTask({
        taskName,
        platform,
        keywords,
        config: validatedConfig,
        priority: priority || 0,
        maxPages: maxPages || 5,
        createdBy: userId
      });

      ctx.body = {
        success: true,
        message: '爬虫任务创建成功',
        data: task
      };
    } catch (error) {
      console.error('创建爬虫任务失败:', error);

      // 如果是配置验证错误，返回详细错误信息
      if (error.name === 'ConfigValidationError') {
        ctx.status = 400;
        ctx.body = {
          success: false,
          message: '配置参数验证失败',
          error: error.message,
          details: error.details || null
        };
        return;
      }

      ctx.throw(400, error.message);
    }
  }

  /**
   * 更新爬虫任务
   *
   * 功能说明：
   * - 更新现有的爬虫任务信息
   * - 只允许更新未启动、已暂停或已取消的任务
   * - 验证参数有效性和任务状态
   * - 支持更新任务配置和优先级
   *
   * 请求体参数：
   * @param {string} taskName - 任务名称（可选）
   * @param {string} platform - 平台类型（可选）
   * @param {string} keywords - 搜索关键词（可选）
   * @param {number} maxPages - 最大页数（可选）
   * @param {number} priority - 任务优先级（可选）
   * @param {Object} config - 爬取配置（可选）
   */
  async updateTask(ctx) {
    try {
      const { id } = ctx.params;
      const { taskName, platform, keywords, maxPages, priority, config } = ctx.request.body;

      // 验证任务是否存在
      const existingTask = await CrawlTask.findByPk(id);
      if (!existingTask) {
        ctx.throw(404, '任务不存在');
      }

      // 检查任务状态，只允许更新特定状态的任务
      const allowedStatuses = ['pending', 'paused', 'cancelled', 'failed'];
      if (!allowedStatuses.includes(existingTask.status)) {
        ctx.throw(
          400,
          `任务状态为 ${existingTask.status}，无法编辑。只能编辑状态为：${allowedStatuses.join('、')} 的任务`
        );
      }

      // 构建更新数据
      const updateData = {};
      if (taskName !== undefined) updateData.taskName = taskName;
      if (platform !== undefined) updateData.platform = platform;
      if (keywords !== undefined) updateData.keywords = keywords;
      if (maxPages !== undefined) updateData.maxPages = maxPages;
      if (priority !== undefined) updateData.priority = priority;

      // 验证和处理配置参数
      if (config !== undefined) {
        updateData.config = this.validateAndProcessConfig(config);
      }

      // 更新任务
      await existingTask.update(updateData);

      // 重新获取更新后的任务数据
      const updatedTask = await CrawlTask.findByPk(id);

      ctx.body = {
        success: true,
        message: '任务更新成功',
        data: updatedTask
      };
    } catch (error) {
      console.error('更新爬虫任务失败:', error);

      // 如果是配置验证错误，返回详细错误信息
      if (error.name === 'ConfigValidationError') {
        ctx.status = 400;
        ctx.body = {
          success: false,
          message: '配置参数验证失败',
          error: error.message,
          details: error.details || null
        };
        return;
      }

      if (error.status) {
        ctx.throw(error.status, error.message);
      } else {
        ctx.throw(400, error.message);
      }
    }
  }

  /**
   * 获取任务列表
   */
  async getTasks(ctx) {
    try {
      const { page = 1, limit = 10, status, platform, keyword } = ctx.query;

      const options = {
        page: parseInt(page),
        limit: parseInt(limit)
      };

      if (status) options.status = status;
      if (platform) options.platform = platform;
      if (keyword) options.keyword = keyword;

      const result = await crawlerService.getTasks(options);

      ctx.body = {
        success: true,
        message: '获取任务列表成功',
        data: result.tasks,
        pagination: result.pagination
      };
    } catch (error) {
      console.error('获取任务列表失败:', error);
      ctx.throw(500, error.message);
    }
  }

  /**
   * 获取任务详情
   */
  async getTaskDetail(ctx) {
    try {
      const { id } = ctx.params;
      const task = await crawlerService.getTaskDetail(parseInt(id));

      ctx.body = {
        success: true,
        message: '获取任务详情成功',
        data: task
      };
    } catch (error) {
      console.error('获取任务详情失败:', error);
      ctx.throw(404, error.message);
    }
  }

  /**
   * 启动任务
   */
  async startTask(ctx) {
    try {
      const { id } = ctx.params;
      const task = await crawlerService.startTask(parseInt(id));

      ctx.body = {
        success: true,
        message: '任务启动成功',
        data: task
      };
    } catch (error) {
      console.error('启动任务失败:', error);
      ctx.throw(400, error.message);
    }
  }

  /**
   * 停止任务（暂停）
   */
  async stopTask(ctx) {
    try {
      const { id } = ctx.params;
      const task = await crawlerService.stopTask(parseInt(id));

      ctx.body = {
        success: true,
        message: '任务暂停成功',
        data: task
      };
    } catch (error) {
      console.error('暂停任务失败:', error);
      ctx.throw(400, error.message);
    }
  }

  /**
   * 强制停止任务（用于处理状态异常的情况）
   */
  async forceStopTask(ctx) {
    try {
      const { id } = ctx.params;
      const task = await crawlerService.forceStopTask(parseInt(id));

      ctx.body = {
        success: true,
        message: '任务强制停止成功',
        data: task
      };
    } catch (error) {
      console.error('强制停止任务失败:', error);
      ctx.throw(400, error.message);
    }
  }

  /**
   * 诊断任务状态（用于调试）
   */
  async diagnoseTask(ctx) {
    try {
      const { id } = ctx.params;
      const diagnosis = await crawlerService.diagnoseTaskStatus(parseInt(id));

      ctx.body = {
        success: true,
        message: '任务状态诊断完成',
        data: diagnosis
      };
    } catch (error) {
      console.error('诊断任务状态失败:', error);
      ctx.throw(400, error.message);
    }
  }

  /**
   * 检查和修复所有状态不一致的任务
   */
  async checkInconsistentTasks(ctx) {
    try {
      const result = await crawlerService.checkAndFixInconsistentTasks();

      ctx.body = {
        success: true,
        message: '状态不一致任务检查完成',
        data: result
      };
    } catch (error) {
      console.error('检查状态不一致任务失败:', error);
      ctx.throw(400, error.message);
    }
  }

  /**
   * 恢复任务
   */
  async resumeTask(ctx) {
    try {
      const { id } = ctx.params;
      const task = await crawlerService.resumeTask(parseInt(id));

      ctx.body = {
        success: true,
        message: '任务恢复成功',
        data: task
      };
    } catch (error) {
      console.error('恢复任务失败:', error);
      ctx.throw(400, error.message);
    }
  }

  /**
   * 重试任务
   */
  async retryTask(ctx) {
    try {
      const { id } = ctx.params;
      const task = await crawlerService.retryTask(parseInt(id));

      ctx.body = {
        success: true,
        message: '任务重试成功',
        data: task
      };
    } catch (error) {
      console.error('重试任务失败:', error);
      ctx.throw(400, error.message);
    }
  }
  /**
   * 删除任务
   */
  async deleteTask(ctx) {
    try {
      const { id } = ctx.params;
      await crawlerService.deleteTask(parseInt(id));

      ctx.body = {
        success: true,
        message: '任务删除成功'
      };
    } catch (error) {
      console.error('删除任务失败:', error);
      ctx.throw(400, error.message);
    }
  }

  /**
   * 批量删除任务
   */
  async batchDeleteTasks(ctx) {
    try {
      const { ids } = ctx.request.body;

      if (!Array.isArray(ids) || ids.length === 0) {
        ctx.throw(400, '请选择要删除的任务');
      }

      let deletedCount = 0;
      const errors = [];

      for (const id of ids) {
        try {
          await crawlerService.deleteTask(parseInt(id));
          deletedCount++;
        } catch (error) {
          errors.push({ id, error: error.message });
        }
      }

      ctx.body = {
        success: true,
        message: `成功删除 ${deletedCount} 个任务`,
        data: {
          deletedCount,
          errors
        }
      };
    } catch (error) {
      console.error('批量删除任务失败:', error);
      ctx.throw(400, error.message);
    }
  }

  /**
   * 批量重试任务
   */
  async batchRetryTasks(ctx) {
    try {
      const { ids } = ctx.request.body;

      if (!Array.isArray(ids) || ids.length === 0) {
        ctx.throw(400, '请选择要重试的任务');
      }

      const result = await crawlerService.batchRetryTasks(ids);

      ctx.body = {
        success: true,
        message: `成功重试 ${result.successCount} 个任务`,
        data: result
      };
    } catch (error) {
      console.error('批量重试任务失败:', error);
      ctx.throw(400, error.message);
    }
  }
  /**
   * 获取任务结果
   */
  async getTaskResults(ctx) {
    try {
      const { id } = ctx.params;
      const { page = 1, limit = 20, status, platform, keyword } = ctx.query;

      const where = { taskId: parseInt(id) };

      // 状态筛选
      if (status) where.status = status;

      // 平台筛选
      if (platform) where.platform = platform;

      // 关键词搜索
      if (keyword) {
        where[Op.or] = [
          { nickname: { [Op.like]: `%${keyword}%` } },
          { platformUserId: { [Op.like]: `%${keyword}%` } },
          { uniqueId: { [Op.like]: `%${keyword}%` } }
        ];
      }

      const result = await PublicInfluencer.findAndCountAll({
        where,
        attributes: PublicInfluencer.SAFE_ATTRIBUTES, // 排除敏感字段
        include: [
          {
            model: CrawlTask,
            as: 'crawlTask',
            attributes: ['id', 'taskName', 'platform']
          }
        ],
        order: [['createdAt', 'DESC']],
        limit: parseInt(limit),
        offset: (parseInt(page) - 1) * parseInt(limit)
      });

      // 计算统计信息（基于整个任务，不受筛选条件影响）
      const stats = await PublicInfluencer.findAll({
        where: { taskId: parseInt(id) },
        attributes: ['status', [dbInstance.fn('COUNT', dbInstance.col('id')), 'count']],
        group: ['status'],
        raw: true
      });

      const statsMap = stats.reduce((acc, stat) => {
        acc[stat.status] = parseInt(stat.count);
        return acc;
      }, {});

      // 计算整个任务的总记录数（不受筛选条件影响）
      const totalTaskResults = Object.values(statsMap).reduce((sum, count) => sum + count, 0);

      const responseData = {
        list: DateFormatter.formatArray(result.rows),
        pagination: {
          total: result.count, // 筛选后的结果数量，用于分页
          page: parseInt(page),
          limit: parseInt(limit),
          pages: Math.ceil(result.count / parseInt(limit))
        },
        stats: {
          total: totalTaskResults, // 整个任务的总记录数
          pending: statsMap.pending || 0,
          processed: statsMap.processed || 0,
          imported: statsMap.imported || 0,
          failed: statsMap.failed || 0
        }
      };

      ResponseUtil.success(ctx, responseData, '获取任务结果成功');
    } catch (error) {
      console.error('获取任务结果失败:', error);
      ctx.throw(500, error.message);
    }
  }

  /**
   * 获取任务日志
   */
  async getTaskLogs(ctx) {
    try {
      const { id } = ctx.params;
      const { page = 1, limit = 50, level } = ctx.query;

      const where = { taskId: parseInt(id) };
      if (level) where.level = level;

      const result = await CrawlLog.findAndCountAll({
        where,
        order: [['createdAt', 'DESC']],
        limit: parseInt(limit),
        offset: (parseInt(page) - 1) * parseInt(limit)
      });

      ResponseUtil.sequelizePagination(ctx, result, { page, limit }, '获取任务日志成功');
    } catch (error) {
      console.error('获取任务日志失败:', error);
      ctx.throw(500, error.message);
    }
  }

  /**
   * 导入整个爬虫任务的结果到达人表
   */
  async importTaskResults(ctx) {
    try {
      console.log('🔍 [任务导入] 开始执行，用户:', ctx.state.user?.id);
      const { id } = ctx.params;
      const taskId = parseInt(id);
      console.log('🔍 [任务导入] 参数检查 - taskId:', taskId);

      // 验证任务ID
      if (!taskId || isNaN(taskId)) {
        console.log('❌ [任务导入] 参数验证失败 - taskId无效');
        ctx.status = 200;
        ctx.body = {
          success: false,
          message: '无效的任务ID'
        };
        return;
      }

      // 检查任务是否存在
      const { CrawlTask } = require('../models');
      const task = await CrawlTask.findByPk(taskId);
      if (!task) {
        console.log('❌ [任务导入] 任务不存在 - taskId:', taskId);
        ctx.status = 200;
        ctx.body = {
          success: false,
          message: '任务不存在'
        };
        return;
      }

      console.log('🔍 [任务导入] 任务信息:', {
        id: task.id,
        name: task.taskName,
        platform: task.platform,
        status: task.status
      });

      // 首先获取该任务下所有的爬虫结果（用于统计）
      const allResults = await PublicInfluencer.findAll({
        where: {
          taskId: taskId
        },
        attributes: ['id', 'status', 'nickname', 'platformUserId']
      });

      console.log('🔍 [任务导入] 任务下所有结果统计:', {
        total: allResults.length,
        byStatus: allResults.reduce((acc, r) => {
          acc[r.status] = (acc[r.status] || 0) + 1;
          return acc;
        }, {})
      });

      // 获取该任务下所有状态为'processed'的爬虫结果
      const results = await PublicInfluencer.findAll({
        where: {
          taskId: taskId,
          status: 'processed'
        },
        attributes: PublicInfluencer.SAFE_ATTRIBUTES // 排除敏感字段
      });

      console.log(
        '🔍 [任务导入] 找到的可导入记录:',
        results.map(r => ({
          id: r.id,
          status: r.status,
          nickname: r.nickname,
          platformUserId: r.platformUserId
        }))
      );

      if (results.length === 0) {
        console.log('❌ [任务导入] 无可导入的结果');

        // 提供更详细的错误信息
        let detailedMessage = '该任务没有可导入的结果';
        if (allResults.length === 0) {
          detailedMessage = '该任务没有任何爬虫结果数据，请先执行爬虫任务';
        } else {
          const statusCounts = allResults.reduce((acc, r) => {
            acc[r.status] = (acc[r.status] || 0) + 1;
            return acc;
          }, {});

          const statusMessages = [];
          if (statusCounts.pending) statusMessages.push(`${statusCounts.pending}个待处理`);
          if (statusCounts.failed) statusMessages.push(`${statusCounts.failed}个失败`);
          if (statusCounts.imported) statusMessages.push(`${statusCounts.imported}个已导入`);

          if (statusMessages.length > 0) {
            detailedMessage = `该任务共有${allResults.length}条结果，但没有可导入的记录（${statusMessages.join(
              '，'
            )}）`;
          }
        }

        ctx.status = 200;
        ctx.body = {
          success: false,
          message: detailedMessage,
          data: {
            taskId,
            taskName: task.taskName,
            totalResults: allResults.length,
            statusBreakdown: allResults.reduce((acc, r) => {
              acc[r.status] = (acc[r.status] || 0) + 1;
              return acc;
            }, {}),
            availableForImport: 0
          }
        };
        return;
      }

      const { MyInfluencer } = require('../models');
      let successCount = 0;
      let skipCount = 0;
      const errors = [];

      for (const result of results) {
        try {
          console.log('🔍 [任务导入] 处理结果:', {
            id: result.id,
            platform: result.platform,
            platformUserId: result.platformUserId,
            nickname: result.nickname
          });

          // 检查是否已存在相同的达人
          const existingInfluencer = await MyInfluencer.findOne({
            where: {
              platform: result.platform,
              platformId: result.platformUserId // 注意：Influencer表中字段名是platformId
            }
          });

          if (existingInfluencer) {
            console.log('⚠️ [任务导入] 达人已存在，跳过:', {
              resultId: result.id,
              nickname: result.nickname,
              existingInfluencerId: existingInfluencer.id
            });
            skipCount++;
            continue;
          }

          // 创建达人记录
          const influencerData = {
            platform: result.platform,
            platformId: result.platformUserId, // 注意：Influencer表中字段名是platformId
            nickname: result.nickname,
            avatarUrl: result.avatarUrl,
            followersCount: result.followersCount,
            city: result.city,
            uniqueId: result.uniqueId,
            contactInfo: result.contactInfo,
            videoStats: result.videoStats,
            authorExtInfo: result.authorExtInfo, // 添加扩展信息字段
            contentTheme: result.contentTheme, // 添加内容主题字段
            influencerTags: result.influencerTags, // 添加达人标签字段
            rawData: result.rawData,
            status: 'active',
            source: 'crawler',
            sourceTaskId: result.taskId,
            notes: `从爬虫任务 ${task.taskName} (ID: ${taskId}) 导入`
          };

          const influencer = await MyInfluencer.create(influencerData);

          // 更新爬虫结果状态
          await result.update({
            status: 'imported',
            importedInfluencerId: influencer.id
          });

          console.log('✅ [任务导入] 成功导入:', {
            resultId: result.id,
            influencerId: influencer.id,
            nickname: result.nickname
          });

          successCount++;
        } catch (error) {
          console.error('❌ [任务导入] 导入失败:', {
            resultId: result.id,
            nickname: result.nickname,
            error: error.message
          });

          errors.push({
            resultId: result.id,
            nickname: result.nickname,
            platformUserId: result.platformUserId,
            error: error.message
          });
        }
      }

      // 构建易读的错误信息字符串
      let errorMessage = '';
      if (errors.length > 0) {
        const errorDetails = errors
          .map(err => {
            const nickname = err.nickname || '未知';
            const platformUserId = err.platformUserId || '未知';
            return `• 结果ID ${err.resultId} (${nickname}, 平台ID: ${platformUserId}): ${err.error}`;
          })
          .join('\n');

        errorMessage = `以下 ${errors.length} 个记录导入失败：\n${errorDetails}`;
      }

      // 构建响应消息
      let responseMessage = `成功导入 ${successCount} 个结果`;
      if (skipCount > 0) {
        responseMessage += `，跳过 ${skipCount} 个已存在的达人`;
      }
      if (errors.length > 0) {
        responseMessage += `，${errors.length} 个失败`;
      }

      console.log('🎉 [任务导入] 完成统计:', {
        taskId,
        taskName: task.taskName,
        totalFound: results.length,
        successCount,
        skipCount,
        failedCount: errors.length
      });

      ctx.body = {
        success: true,
        message: responseMessage,
        data: {
          taskId,
          taskName: task.taskName,
          totalFound: results.length,
          successCount,
          skipCount,
          failedCount: errors.length,
          totalProcessed: results.length,
          errors: errors, // 保留原始错误数组供前端详细处理
          errorMessage: errorMessage // 新增：易读的错误信息字符串
        }
      };
    } catch (error) {
      console.error('❌ [任务导入] 系统错误:', error);
      ctx.status = 500;
      ctx.body = {
        success: false,
        message: '导入过程中发生系统错误',
        error: error.message
      };
    }
  }

  /**
   * 获取爬虫服务状态
   */
  async getServiceStatus(ctx) {
    try {
      const status = crawlerService.getStatus();

      ctx.body = {
        success: true,
        message: '获取服务状态成功',
        data: status
      };
    } catch (error) {
      console.error('获取服务状态失败:', error);
      ctx.throw(500, error.message);
    }
  }

  /**
   * 获取统计信息
   */
  async getStats(ctx) {
    try {
      // 任务统计
      const taskStats = await CrawlTask.findAll({
        attributes: ['status', [require('sequelize').fn('COUNT', '*'), 'count']],
        group: ['status']
      });

      // 平台统计
      const platformStats = await CrawlTask.findAll({
        attributes: ['platform', [require('sequelize').fn('COUNT', '*'), 'count']],
        group: ['platform']
      });

      // 结果统计
      const resultStats = await PublicInfluencer.findAll({
        attributes: ['platform', [require('sequelize').fn('COUNT', '*'), 'count']],
        group: ['platform']
      });

      // 今日任务数
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const todayTaskCount = await CrawlTask.count({
        where: {
          createdAt: {
            [Op.gte]: today
          }
        }
      });

      // 计算前端需要的统计数据格式
      const statsMap = taskStats.reduce((acc, item) => {
        acc[item.status] = parseInt(item.dataValues.count);
        return acc;
      }, {});

      // 计算总任务数
      const totalTasks = Object.values(statsMap).reduce((sum, count) => sum + count, 0);

      ctx.body = {
        success: true,
        message: '获取统计信息成功',
        data: {
          // 前端期望的格式
          total: totalTasks,
          running: statsMap.running || 0,
          completed: statsMap.completed || 0,
          failed: statsMap.failed || 0,
          pending: statsMap.pending || 0,
          paused: statsMap.paused || 0,
          cancelled: statsMap.cancelled || 0,

          // 原始详细数据（保持向后兼容）
          taskStats: taskStats.map(item => ({
            status: item.status,
            count: parseInt(item.dataValues.count)
          })),
          platformStats: platformStats.map(item => ({
            platform: item.platform,
            count: parseInt(item.dataValues.count)
          })),
          resultStats: resultStats.map(item => ({
            platform: item.platform,
            count: parseInt(item.dataValues.count)
          })),
          todayTaskCount
        }
      };
    } catch (error) {
      console.error('获取统计信息失败:', error);
      ctx.throw(500, error.message);
    }
  }

  /**
   * 获取结果详情
   */
  async getResultDetail(ctx) {
    try {
      const { id } = ctx.params;

      const result = await PublicInfluencer.findByPk(id, {
        attributes: PublicInfluencer.SAFE_ATTRIBUTES, // 排除敏感字段
        include: [
          {
            model: CrawlTask,
            as: 'crawlTask',
            attributes: ['id', 'taskName', 'platform']
          }
        ]
      });

      if (!result) {
        ctx.throw(404, '结果不存在');
      }

      ctx.body = {
        success: true,
        data: result
      };
    } catch (error) {
      console.error('获取结果详情失败:', error);
      ctx.throw(500, error.message);
    }
  }

  /**
   * 批量导入结果到达人库
   */
  async batchImportResults(ctx) {
    try {
      console.log('🔍 [批量导入] 开始执行，用户:', ctx.state.user?.id);
      const { ids } = ctx.request.body;
      console.log('🔍 [批量导入] 参数检查 - ids:', ids);

      if (!Array.isArray(ids) || ids.length === 0) {
        console.log('❌ [批量导入] 参数验证失败 - ids无效');
        ctx.status = 200;
        ctx.body = {
          success: false,
          message: '请选择要导入的结果'
        };
        return;
      }

      // 调试信息：记录传入的ID
      console.log('批量导入 - 传入的IDs:', ids);
      console.log(
        '批量导入 - IDs类型:',
        ids.map(id => typeof id)
      );

      // 确保ID为整数类型
      const numericIds = ids.map(id => parseInt(id)).filter(id => !isNaN(id));
      console.log('批量导入 - 转换后的IDs:', numericIds);

      if (numericIds.length === 0) {
        ctx.status = 200;
        ctx.body = {
          success: false,
          message: '无效的ID格式'
        };
        return;
      }

      // 查询所有匹配的记录（包含完整数据用于导入）
      const results = await PublicInfluencer.findAll({
        where: {
          id: { [Op.in]: numericIds }
        },
        attributes: PublicInfluencer.SAFE_ATTRIBUTES // 排除敏感字段
      });

      console.log(
        '批量导入 - 找到的记录:',
        results.map(r => ({ id: r.id, status: r.status, nickname: r.nickname }))
      );

      if (results.length === 0) {
        ctx.status = 200;
        ctx.body = {
          success: false,
          message: '选中的ID在数据库中不存在'
        };
        return;
      }

      // 筛选出可导入的记录（排除已导入的）
      const importableResults = results.filter(result => result.status !== 'imported');

      console.log('批量导入 - 可导入的记录数量:', importableResults.length);

      if (importableResults.length === 0) {
        ctx.status = 200;
        ctx.body = {
          success: false,
          message: '选中的记录都已经导入过了'
        };
        return;
      }

      const { MyInfluencer } = require('../models');
      let successCount = 0;
      const errors = [];

      for (const result of importableResults) {
        try {
          console.log('批量导入 - 处理结果:', {
            id: result.id,
            platform: result.platform,
            platformUserId: result.platformUserId,
            nickname: result.nickname
          });

          // 检查是否已存在相同的达人
          const existingInfluencer = await MyInfluencer.findOne({
            where: {
              platform: result.platform,
              platformId: result.platformUserId // 注意：Influencer表中字段名是platformId
            }
          });

          if (existingInfluencer) {
            errors.push({
              resultId: result.id,
              nickname: result.nickname,
              platformUserId: result.platformUserId,
              error: `该达人已存在于达人库中 (达人库ID: ${existingInfluencer.id})`
            });
            continue;
          }

          // 创建达人记录
          const influencerData = {
            platform: result.platform,
            platformId: result.platformUserId, // 注意：Influencer表中字段名是platformId
            nickname: result.nickname,
            avatarUrl: result.avatarUrl,
            followersCount: result.followersCount,
            city: result.city,
            uniqueId: result.uniqueId,
            contactInfo: result.contactInfo,
            videoStats: result.videoStats,
            authorExtInfo: result.authorExtInfo, // 添加扩展信息字段
            contentTheme: result.contentTheme, // 添加内容主题字段
            influencerTags: result.influencerTags, // 添加达人标签字段
            rawData: result.rawData,
            status: 'active',
            source: 'crawler',
            sourceTaskId: result.taskId
          };

          await MyInfluencer.create(influencerData);
          await result.update({ status: 'imported' });
          successCount++;
        } catch (error) {
          errors.push({
            resultId: result.id,
            nickname: result.nickname,
            platformUserId: result.platformUserId,
            error: error.message
          });
        }
      }

      // 构建易读的错误信息字符串
      let errorMessage = '';
      if (errors.length > 0) {
        const errorDetails = errors
          .map(err => {
            // 使用错误对象中已包含的信息，避免重复查找
            const nickname = err.nickname || '未知';
            const platformUserId = err.platformUserId || '未知';

            return `• 结果ID ${err.resultId} (${nickname}, 平台ID: ${platformUserId}): ${err.error}`;
          })
          .join('\n');

        errorMessage = `以下 ${errors.length} 个记录导入失败：\n${errorDetails}`;
      }

      // 构建响应消息
      let responseMessage = `成功导入 ${successCount} 个结果`;
      if (errors.length > 0) {
        responseMessage += `，${errors.length} 个失败`;
      }

      ctx.body = {
        success: true,
        message: responseMessage,
        data: {
          successCount,
          failedCount: errors.length,
          totalProcessed: importableResults.length,
          errors: errors, // 保留原始错误数组供前端详细处理
          errorMessage: errorMessage // 新增：易读的错误信息字符串
        }
      };
    } catch (error) {
      console.error('批量导入结果失败:', error);
      ctx.throw(500, error.message);
    }
  }

  /**
   * 导出结果数据
   */
  async exportResults(ctx) {
    try {
      const { taskId, keyword, platform, status } = ctx.query;

      const where = {};
      if (taskId) where.taskId = taskId;
      if (platform) where.platform = platform;
      if (status) where.status = status;
      if (keyword) {
        where[Op.or] = [
          { nickname: { [Op.like]: `%${keyword}%` } },
          { platformUserId: { [Op.like]: `%${keyword}%` } },
          { uniqueId: { [Op.like]: `%${keyword}%` } }
        ];
      }

      const results = await PublicInfluencer.findAll({
        where,
        attributes: PublicInfluencer.SAFE_ATTRIBUTES, // 排除敏感字段
        include: [
          {
            model: CrawlTask,
            as: 'crawlTask',
            attributes: ['taskName', 'platform']
          }
        ],
        order: [['createdAt', 'DESC']]
      });

      // 生成Excel文件
      const ExcelJS = require('exceljs');
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet('爬虫结果');

      // 设置表头
      worksheet.columns = [
        { header: 'ID', key: 'id', width: 10 },
        { header: '任务名称', key: 'taskName', width: 20 },
        { header: '平台', key: 'platform', width: 15 },
        { header: '达人昵称', key: 'nickname', width: 20 },
        { header: '平台用户ID', key: 'platformUserId', width: 20 },
        { header: '唯一标识', key: 'uniqueId', width: 20 },
        { header: '粉丝数量', key: 'followersCount', width: 15 },
        { header: '所在城市', key: 'city', width: 15 },
        { header: '状态', key: 'status', width: 10 },
        { header: '创建时间', key: 'createdAt', width: 20 }
      ];

      // 添加数据
      results.forEach(result => {
        worksheet.addRow({
          id: result.id,
          taskName: result.task?.taskName || '',
          platform: result.platform === 'xiaohongshu' ? '小红书' : '巨量星图',
          nickname: result.nickname,
          platformUserId: result.platformUserId,
          uniqueId: result.uniqueId,
          followersCount: result.followersCount,
          city: result.city,
          status: this.getStatusText(result.status),
          createdAt: result.createdAt
        });
      });

      // 设置响应头
      ctx.set({
        'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Disposition': `attachment; filename="crawler-results-${Date.now()}.xlsx"`
      });

      // 输出Excel文件
      ctx.body = await workbook.xlsx.writeBuffer();
    } catch (error) {
      console.error('导出结果失败:', error);
      ctx.throw(500, error.message);
    }
  }

  /**
   * 获取状态文本
   */
  getStatusText(status) {
    const statusMap = {
      pending: '待处理',
      processed: '已处理',
      imported: '已导入',
      failed: '失败'
    };
    return statusMap[status] || status;
  }

  /**
   * 验证和处理配置参数
   *
   * 功能说明：
   * - 支持任意JSON结构的配置参数
   * - 提供详细的格式验证和错误提示
   * - 确保配置参数的类型安全
   * - 支持嵌套对象、数组等复杂结构
   *
   * @param {any} config - 配置参数
   * @returns {Object} 验证后的配置对象
   * @throws {ConfigValidationError} 配置验证失败时抛出
   */
  validateAndProcessConfig(config) {
    try {
      // 如果配置为空或undefined，返回空对象
      if (config === null || config === undefined) {
        return {};
      }

      // 如果是字符串，尝试解析为JSON
      if (typeof config === 'string') {
        if (config.trim() === '') {
          return {};
        }

        try {
          const parsed = JSON.parse(config);
          return this.validateConfigObject(parsed);
        } catch (parseError) {
          const error = new Error(`配置参数JSON格式错误: ${parseError.message}`);
          error.name = 'ConfigValidationError';
          error.details = {
            type: 'JSON_PARSE_ERROR',
            originalError: parseError.message,
            input: config.substring(0, 200) + (config.length > 200 ? '...' : '')
          };
          throw error;
        }
      }

      // 如果已经是对象，直接验证
      if (typeof config === 'object') {
        return this.validateConfigObject(config);
      }

      // 其他类型不支持
      const error = new Error(`配置参数类型错误: 期望对象或JSON字符串，实际收到 ${typeof config}`);
      error.name = 'ConfigValidationError';
      error.details = {
        type: 'TYPE_ERROR',
        expectedType: 'object or JSON string',
        actualType: typeof config,
        value: config
      };
      throw error;
    } catch (error) {
      // 如果已经是ConfigValidationError，直接抛出
      if (error.name === 'ConfigValidationError') {
        throw error;
      }

      // 其他错误包装为ConfigValidationError
      const wrappedError = new Error(`配置参数处理失败: ${error.message}`);
      wrappedError.name = 'ConfigValidationError';
      wrappedError.details = {
        type: 'PROCESSING_ERROR',
        originalError: error.message
      };
      throw wrappedError;
    }
  }

  /**
   * 验证配置对象的结构和内容
   *
   * @param {any} configObj - 配置对象
   * @returns {Object} 验证后的配置对象
   * @throws {ConfigValidationError} 验证失败时抛出
   */
  validateConfigObject(configObj) {
    // 必须是对象且不能是数组
    if (!configObj || typeof configObj !== 'object' || Array.isArray(configObj)) {
      const error = new Error('配置参数必须是一个对象，不能是数组或基本类型');
      error.name = 'ConfigValidationError';
      error.details = {
        type: 'STRUCTURE_ERROR',
        expectedType: 'object',
        actualType: Array.isArray(configObj) ? 'array' : typeof configObj,
        value: configObj
      };
      throw error;
    }

    // 验证配置对象的深度（防止过度嵌套）
    const maxDepth = 10;
    const depth = this.getObjectDepth(configObj);
    if (depth > maxDepth) {
      const error = new Error(`配置参数嵌套层级过深: 最大支持${maxDepth}层，当前${depth}层`);
      error.name = 'ConfigValidationError';
      error.details = {
        type: 'DEPTH_ERROR',
        maxDepth,
        actualDepth: depth
      };
      throw error;
    }

    // 验证配置对象的大小（防止过大的配置）
    const configStr = JSON.stringify(configObj);
    const maxSize = 50000; // 50KB
    if (configStr.length > maxSize) {
      const error = new Error(`配置参数过大: 最大支持${maxSize}字符，当前${configStr.length}字符`);
      error.name = 'ConfigValidationError';
      error.details = {
        type: 'SIZE_ERROR',
        maxSize,
        actualSize: configStr.length
      };
      throw error;
    }

    // 验证特定字段的类型（可选的类型检查）
    this.validateSpecificFields(configObj);

    // 返回验证通过的配置对象
    return configObj;
  }

  /**
   * 计算对象的嵌套深度
   *
   * @param {any} obj - 要计算深度的对象
   * @param {number} currentDepth - 当前深度
   * @returns {number} 对象的最大嵌套深度
   */
  getObjectDepth(obj, currentDepth = 1) {
    if (!obj || typeof obj !== 'object') {
      return currentDepth;
    }

    let maxDepth = currentDepth;
    for (const key in obj) {
      if (obj.hasOwnProperty(key) && typeof obj[key] === 'object' && obj[key] !== null) {
        const depth = this.getObjectDepth(obj[key], currentDepth + 1);
        maxDepth = Math.max(maxDepth, depth);
      }
    }

    return maxDepth;
  }

  /**
   * 验证特定字段的类型和值
   *
   * @param {Object} config - 配置对象
   * @throws {ConfigValidationError} 字段验证失败时抛出
   */
  validateSpecificFields(config) {
    const validationRules = {
      // 数值类型字段
      pageSize: { type: 'number', min: 1, max: 100 },
      maxPages: { type: 'number', min: 1, max: 50 },
      retries: { type: 'number', min: 0, max: 10 },
      minFirstNotePlayCount: { type: 'number', min: 0 },
      searchType: { type: 'number', min: 0 },

      // 布尔类型字段
      saveVideos: { type: 'boolean' },
      enableProxy: { type: 'boolean' },

      // 对象类型字段
      delay: { type: 'object' },
      filters: { type: 'object' }

      // 数组类型字段
      // 注意：移除了location的严格类型检查，因为它可能是数组、字符串或对象
    };

    for (const [fieldName, rule] of Object.entries(validationRules)) {
      if (config.hasOwnProperty(fieldName)) {
        const value = config[fieldName];

        // 类型检查
        if (rule.type === 'number' && typeof value !== 'number') {
          const error = new Error(`字段 "${fieldName}" 必须是数字类型，当前类型: ${typeof value}`);
          error.name = 'ConfigValidationError';
          error.details = {
            type: 'FIELD_TYPE_ERROR',
            fieldName,
            expectedType: 'number',
            actualType: typeof value,
            value
          };
          throw error;
        }

        if (rule.type === 'boolean' && typeof value !== 'boolean') {
          const error = new Error(`字段 "${fieldName}" 必须是布尔类型，当前类型: ${typeof value}`);
          error.name = 'ConfigValidationError';
          error.details = {
            type: 'FIELD_TYPE_ERROR',
            fieldName,
            expectedType: 'boolean',
            actualType: typeof value,
            value
          };
          throw error;
        }

        if (rule.type === 'object' && (typeof value !== 'object' || Array.isArray(value) || value === null)) {
          const error = new Error(`字段 "${fieldName}" 必须是对象类型`);
          error.name = 'ConfigValidationError';
          error.details = {
            type: 'FIELD_TYPE_ERROR',
            fieldName,
            expectedType: 'object',
            actualType: Array.isArray(value) ? 'array' : typeof value,
            value
          };
          throw error;
        }

        if (rule.type === 'array' && !Array.isArray(value)) {
          const error = new Error(`字段 "${fieldName}" 必须是数组类型，当前类型: ${typeof value}`);
          error.name = 'ConfigValidationError';
          error.details = {
            type: 'FIELD_TYPE_ERROR',
            fieldName,
            expectedType: 'array',
            actualType: typeof value,
            value
          };
          throw error;
        }

        // 数值范围检查
        if (rule.type === 'number') {
          if (rule.min !== undefined && value < rule.min) {
            const error = new Error(`字段 "${fieldName}" 的值不能小于 ${rule.min}，当前值: ${value}`);
            error.name = 'ConfigValidationError';
            error.details = {
              type: 'FIELD_RANGE_ERROR',
              fieldName,
              minValue: rule.min,
              actualValue: value
            };
            throw error;
          }

          if (rule.max !== undefined && value > rule.max) {
            const error = new Error(`字段 "${fieldName}" 的值不能大于 ${rule.max}，当前值: ${value}`);
            error.name = 'ConfigValidationError';
            error.details = {
              type: 'FIELD_RANGE_ERROR',
              fieldName,
              maxValue: rule.max,
              actualValue: value
            };
            throw error;
          }
        }
      }
    }
  }
}

module.exports = new CrawlerController();
